import { redirectToDashboard } from "@/utils/navigaterole";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

// const getToken = (): string | null => {
//   const localToken = localStorage.getItem("token");
//   if (localToken) return localToken;

//   const cookieMatch = document.cookie.match(/(?:^|;\s*)token=([^;]*)/);
//   return cookieMatch ? cookieMatch[1] : null;
// };

// type AuthRedirectProps = {
//   children: React.ReactNode;
// };

// const AuthRedirect = ({ children }: AuthRedirectProps) => {
//   const navigate = useNavigate();
//   // const role = localStorage.getItem("role");

//   useEffect(() => {
//     const token = getToken();
//     if (token) {
//       // navigate("/dashboard");
//       // TODO - Uncomment the line below to redirect to the dashboard
//       redirectToDashboard("ADMIN", navigate);
//     }
//   }, [navigate]);

//   return <>{children}</>;
// };

// export default AuthRedirect;

const getToken = (): string | null => {
  const localToken = localStorage.getItem("token");
  if (localToken) return localToken;

  const cookieMatch = document.cookie.match(/(?:^|;\s*)token=([^;]*)/);
  return cookieMatch ? cookieMatch[1] : null;
};

type AuthRedirectProps = {
  children: React.ReactNode;
  skipForPaths?: string[]; // Add this prop
};

const AuthRedirect = ({ children, skipForPaths = [] }: AuthRedirectProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.log("🔍 AuthRedirect check:", {
      currentPath: location.pathname,
      skipForPaths,
      hasToken: !!getToken()
    });

    // Skip redirection for specific paths (like password reset)
    if (skipForPaths.some((path) => location.pathname.includes(path))) {
      console.log("⏭️ Skipping redirect for skipForPaths:", location.pathname);
      return;
    }

    // Skip redirection for profile completion and subscription flow paths
    const profileCompletionPaths = [
      "/auth/profile/authprofile",
      "/auth/plan-select",
      "/auth/address-billing",
      "/auth/bank-transfer-payment"
    ];

    if (profileCompletionPaths.some((path) => location.pathname.includes(path))) {
      console.log("⏭️ Skipping redirect for profile completion path:", location.pathname);
      return;
    }

    const token = getToken();
    if (token) {
      console.log("🔄 Token found, redirecting to dashboard");
      // You'll need to get the user's role from somewhere (context, localStorage, etc.)
      const userRole = localStorage.getItem("userRole") || "CLIENT"; // Default or fetch properly
      redirectToDashboard(userRole, navigate);
    }
  }, [navigate, location, skipForPaths]);

  return <>{children}</>;
};

export default AuthRedirect;
